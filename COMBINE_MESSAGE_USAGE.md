# 微信合并转发消息解析功能使用说明

## 功能概述

本次修改为MsgUtils工具类添加了对微信合并转发消息（MsgTypeEnum.COMBINE）的解析支持。合并转发消息是微信中将多条聊天记录合并后转发的特殊消息类型。

## 技术实现

### 消息类型识别

合并转发消息的特征：
- Type: 49 (对应微信的Misc类型消息)
- SubType: 19 (合并转发的子类型)

### 解析逻辑

1. **基础信息解析**：从StrContent字段中的XML内容提取合并转发的标题和描述
2. **内容展示**：将解析出的信息格式化为用户友好的文本
3. **错误处理**：当解析失败时，提供默认的显示内容

### 新增方法

#### `parseCombineMessage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir)`
主要的合并转发消息解析方法，负责：
- 设置消息类型为COMBINE
- 解析XML内容获取基本信息
- 处理压缩内容（预留接口）

#### `parseCombineXmlContent(MsgDTO msgDTO, String xmlContent)`
解析XML格式的消息内容，提取：
- title: 合并转发的标题
- des: 描述信息
- 其他相关字段

#### `extractXmlValue(String xml, String tagName)`
通用的XML标签值提取工具方法，支持：
- 普通XML标签格式：`<tag>value</tag>`
- CDATA格式：`<tag><![CDATA[value]]></tag>`

## 使用示例

### 基本用法

```java
// 解析消息，包括合并转发消息
List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "群ID", false, null);

// 筛选合并转发消息
List<MsgDTO> combineMessages = msgs.stream()
    .filter(msg -> msg.getType() == MsgTypeEnum.COMBINE)
    .collect(Collectors.toList());

// 处理合并转发消息
for (MsgDTO msg : combineMessages) {
    System.out.println("合并转发消息: " + msg.getContent());
    System.out.println("发送时间: " + msg.getMsgTime());
    System.out.println("消息ID: " + msg.getMsgSvrID());
}
```

### 测试用例

```java
@Test
public void testParseCombineMessage() {
    List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom", false, null);
    
    List<MsgDTO> combineMessages = ListUtils.filter(msgs, msg -> msg.getType() == MsgTypeEnum.COMBINE);
    
    System.out.println("找到合并转发消息数量: " + combineMessages.size());
    
    for (MsgDTO msg : combineMessages) {
        System.out.println("合并转发消息 - 时间: " + msg.getMsgTime() 
                + ", msgSvrID: " + msg.getMsgSvrID()
                + ", 内容: " + msg.getContent());
    }
}
```

## 输出格式

解析后的合并转发消息内容格式：
- 有标题时：`"合并转发: [标题内容]"`
- 有描述时：`"合并转发: [描述内容]"`
- 默认情况：`"合并转发消息"`

## 注意事项

1. **兼容性**：此实现基于wechatDataBackup项目的消息结构，确保与该工具的兼容性
2. **错误处理**：当XML解析失败时，不会抛出异常，而是使用默认内容
3. **扩展性**：预留了CompressContent解析接口，可在后续版本中实现详细聊天记录的解析
4. **性能**：使用简单的字符串操作进行XML解析，避免引入额外的XML解析库依赖

## 后续改进方向

1. **详细内容解析**：实现CompressContent中详细聊天记录的解析
2. **结构化数据**：提供更结构化的合并转发消息数据模型
3. **多媒体支持**：支持合并转发中包含的图片、视频等多媒体内容的解析
4. **性能优化**：对于大量合并转发消息的批量处理优化

## 相关文件

- `src/main/java/com/pugwoo/weixin_msg/utils/MsgUtils.java` - 主要实现文件
- `src/test/java/com/pugwoo/weixin_msg/TestMsgUtils.java` - 测试用例
- `src/main/java/com/pugwoo/weixin_msg/enums/MsgTypeEnum.java` - 消息类型枚举
