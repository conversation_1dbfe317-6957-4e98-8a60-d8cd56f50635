package com.pugwoo.weixin_msg;

import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.utils.MsgUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * 用于测试MsgUtils的各项功能
 */
public class TestMsgUtils {

    @Test
    public void testParse() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                false, null);
        System.out.println(msgs.size());
    }

    @Test
    public void testParseMedia() {
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                true, ListUtils.of(4135175694025292375L));
        ListUtils.sortAscNullFirst(msgs, MsgDTO::getMsgTime);
        for (MsgDTO msg : msgs) {
            System.out.println(msg.getMsgTime() + ",msgSvrID:" + msg.getMsgSvrID()
                    + ",type:" + msg.getType() + ",filePath:" + msg.getFilePath()
                    + ", thumb:" + msg.getThumbnailPath());
        }

        // 统计图片中，有filePath的数量和有thumb的数量
        System.out.println("total:" + msgs.size() + ",filePath:" +
                ListUtils.filter(msgs, o -> o.getFilePath() != null).size()
                + ",thumb:" + ListUtils.filter(msgs, o -> o.getThumbnailPath() != null).size()
                + ",none:" + ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).size());

        assert ListUtils.filter(msgs, o -> o.getFilePath() == null && o.getThumbnailPath() == null).isEmpty();
    }

    @Test
    public void testParseCombineMessage() {
        // 测试合并转发消息的解析
        List<MsgDTO> msgs = MsgUtils.parseMsg("E:\\weixin-msg\\User", "24507125117@chatroom",
                false, null);

        // 筛选出合并转发消息
        List<MsgDTO> combineMessages = ListUtils.filter(msgs, msg -> msg.getType() == MsgTypeEnum.COMBINE);

        System.out.println("找到合并转发消息数量: " + combineMessages.size());

        for (MsgDTO msg : combineMessages) {
            System.out.println("合并转发消息 - 时间: " + msg.getMsgTime()
                    + ", msgSvrID: " + msg.getMsgSvrID()
                    + ", 内容: " + msg.getContent());
        }

        // 验证合并转发消息不会抛出异常
        assert combineMessages.size() >= 0; // 可能没有合并转发消息，但不应该抛出异常
    }

}
