package com.pugwoo.weixin_msg.utils;

import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.weixin_msg.dto.MsgDTO;
import com.pugwoo.weixin_msg.entity.MsgDO;
import com.pugwoo.weixin_msg.enums.MsgTypeEnum;
import com.pugwoo.weixin_msg.protobuf.Msg;
import lombok.SneakyThrows;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

public class MsgUtils {

    /**
     * 解析由wechatDataBackup生成的文件中的消息
     *
     * @param decryptMsgDir wechatDataBackup生成的User文件夹的位置【必须】
     * @param groupId 群的id，TODO 待确定能不能通过群id拿到群名称
     * @param isOnlyMedia 是否只处理图片、视频、文件
     * @param ignoredMsgIds 指定的忽略不处理的消息id
     * @return 处理后的消息
     */
    @SneakyThrows
    public static List<MsgDTO> parseMsg(String decryptMsgDir, String groupId, boolean isOnlyMedia, List<Long> ignoredMsgIds) {
        List<MsgDTO> result = new ArrayList<>();

        List<String> dbFiles = DBUtils.listDBFile(decryptMsgDir + "\\pugwoo\\Msg\\Multi", "^MSG.*\\.db");
        for (String dbPath : dbFiles) {
            DBHelper dbHelper = DBUtils.getDBHelper(dbPath);
            List<MsgDO> msgList = dbHelper.getAll(MsgDO.class, "where StrTalker=?", groupId);

            for (MsgDO msg : msgList) {
                if (ignoredMsgIds != null && ignoredMsgIds.contains(msg.getMsgSvrID())) {
                    continue;
                }

                MsgTypeEnum msgTypeEnum = transToMyType(msg.getType(), msg.getSubType());

                if (isOnlyMedia && !(msgTypeEnum == MsgTypeEnum.IMAGE || msgTypeEnum == MsgTypeEnum.VIDEO
                        || msgTypeEnum == MsgTypeEnum.FILE)) {
                    continue; // 只处理图片、视频、文件
                }

                MsgDTO msgDTO = new MsgDTO();
                result.add(msgDTO);
                msgDTO.setMsgTime(from(msg.getCreateTime()));
                msgDTO.setMsgSvrID(msg.getMsgSvrID());
                msgDTO.setContent(msg.getStrContent());
                msgDTO.setType(msgTypeEnum);

                if (msgTypeEnum == MsgTypeEnum.COMBINE) {
                    // 解析合并转发消息
                    parseCombineMessage(msgDTO, msg, decryptMsgDir);
                }

                // 最重要的，解析文件路径
                if (msgTypeEnum == MsgTypeEnum.IMAGE) {
                    parseImage(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.VIDEO) {
                    parseVideo(msgDTO, msg, decryptMsgDir);
                } else if (msgTypeEnum == MsgTypeEnum.FILE) {
                    throw new RuntimeException("TODO 工具尚不支持文件的解析，msgSvrID=" + msg.getMsgSvrID() + "，dbPath:" + dbPath
                            + "，time:" + from(msg.getCreateTime()));
                }
            }
        }

        return result;
    }

    @SneakyThrows
    private static void parseVideo(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isVideoFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".mp4")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isVideoFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }

            if (!isVideoFound) { // 如果视频找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".jpg")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("视频消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    @SneakyThrows
    private static void parseImage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        if (msg.getBytesExtra() != null && msg.getBytesExtra().length > 0) {
            Msg.MessageBytesExtra messageBytesExtra = Msg.MessageBytesExtra.parseFrom(msg.getBytesExtra());
            List<Msg.SubMessage2> message2List = messageBytesExtra.getMessage2List();

            boolean isImageFound = false;
            for (Msg.SubMessage2 message2 : message2List) {
                String field2 = message2.getField2();
                if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("image")
                    && !field2.toLowerCase().contains("thumb")) {
                    String path = decryptMsgDir + "\\" + field2;
                    // 检查文件是否存在，存在才算是真的存在
                    if (FileUtils.isExist(path)) {
                        isImageFound = true;
                        msgDTO.setFilePath(path);
                        msgDTO.setFileUuid(getFileUuid(path));
                        break;
                    }
                }
            }
            if (!isImageFound) { // 如果图片找不到，就找缩略图
                for (Msg.SubMessage2 message2 : message2List) {
                    String field2 = message2.getField2();
                    if (field2.toLowerCase().endsWith(".dat") && field2.toLowerCase().contains("thumb")) {
                        String path = decryptMsgDir + "\\" + field2;
                        // 检查文件是否存在，存在才算是真的存在
                        if (FileUtils.isExist(path)) {
                            msgDTO.setThumbnailPath(path);
                            msgDTO.setFileUuid(getFileUuid(path));
                            break;
                        }
                    }
                }
            }
        } else {
            throw new RuntimeException("图片消息没有bytesExtra，msgSvrID=" + msg.getMsgSvrID());
        }
    }

    private static String getFileUuid(String file) {
        return file.substring(file.lastIndexOf("\\") + 1, file.lastIndexOf("."));
    }

    private static MsgTypeEnum transToMyType(Integer type, Integer subType) {
        if (type == null) {
            return MsgTypeEnum.UNKNOWN;
        }
        if (type.equals(0)) {
            return MsgTypeEnum.TEXT;
        }
        if (type.equals(3)) {
            return MsgTypeEnum.IMAGE;
        }
        if (type.equals(43)) {
            return MsgTypeEnum.VIDEO;
        }
        if (type.equals(49) && subType != null && subType.equals(6)) {
            return MsgTypeEnum.FILE;
        }
        if (type.equals(49) && subType != null && subType.equals(19)) {
            return MsgTypeEnum.COMBINE;
        }
        return MsgTypeEnum.OTHERS;
    }

    private static LocalDateTime from(Long creatTime) {
        Instant instant = Instant.ofEpochSecond(creatTime);
        ZoneId zoneId = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zoneId);
    }

    /**
     * 解析合并转发消息
     * 合并转发消息的结构：
     * - Type: 49 (Misc类型)
     * - SubType: 19 (合并转发)
     * - StrContent: 包含XML格式的消息内容，其中包含合并转发的标题和摘要信息
     * - CompressContent: 可能包含压缩的详细聊天记录数据
     *
     * @param msgDTO 要填充的消息DTO
     * @param msg 原始消息数据
     * @param decryptMsgDir 解密消息目录
     */
    @SneakyThrows
    private static void parseCombineMessage(MsgDTO msgDTO, MsgDO msg, String decryptMsgDir) {
        // 设置消息类型为合并转发
        msgDTO.setType(MsgTypeEnum.COMBINE);

        // 解析StrContent中的XML内容
        String strContent = msg.getStrContent();
        if (strContent != null && !strContent.isEmpty()) {
            try {
                // 解析XML内容获取合并转发的基本信息
                parseCombineXmlContent(msgDTO, strContent);
            } catch (Exception e) {
                // 如果XML解析失败，使用原始内容
                msgDTO.setContent("合并转发消息: " + strContent);
            }
        } else {
            msgDTO.setContent("合并转发消息");
        }

        // 尝试解析CompressContent获取详细的聊天记录
        if (msg.getCompressContent() != null && msg.getCompressContent().length > 0) {
            try {
                parseCombineCompressContent(msgDTO, msg.getCompressContent());
            } catch (Exception e) {
                // 如果解析失败，记录错误但不抛出异常
                System.err.println("解析合并转发消息的CompressContent失败: " + e.getMessage());
            }
        }
    }

    /**
     * 解析合并转发消息的XML内容
     * XML结构通常包含：
     * - title: 合并转发的标题
     * - des: 描述信息
     * - recorditem: 聊天记录摘要
     */
    private static void parseCombineXmlContent(MsgDTO msgDTO, String xmlContent) {
        try {
            // 简单的XML解析，提取title和des字段
            String title = extractXmlValue(xmlContent, "title");
            String des = extractXmlValue(xmlContent, "des");

            if (title != null && !title.isEmpty()) {
                msgDTO.setContent("合并转发: " + title);
            } else if (des != null && !des.isEmpty()) {
                msgDTO.setContent("合并转发: " + des);
            } else {
                msgDTO.setContent("合并转发消息");
            }
        } catch (Exception e) {
            msgDTO.setContent("合并转发消息");
        }
    }

    /**
     * 解析合并转发消息的压缩内容
     * 这部分可能包含实际的聊天记录数据
     */
    private static void parseCombineCompressContent(MsgDTO msgDTO, byte[] compressContent) {
        // 目前暂不解析压缩内容中的详细聊天记录
        // 这需要更复杂的解压缩和protobuf解析逻辑
        // 可以在后续版本中实现
    }

    /**
     * 从XML字符串中提取指定标签的值
     */
    private static String extractXmlValue(String xml, String tagName) {
        String startTag = "<" + tagName + ">";
        String endTag = "</" + tagName + ">";

        int startIndex = xml.indexOf(startTag);
        if (startIndex == -1) {
            // 尝试查找带CDATA的格式
            startTag = "<" + tagName + "><![CDATA[";
            endTag = "]]></" + tagName + ">";
            startIndex = xml.indexOf(startTag);
            if (startIndex == -1) {
                return null;
            }
        }

        int endIndex = xml.indexOf(endTag, startIndex);
        if (endIndex == -1) {
            return null;
        }

        return xml.substring(startIndex + startTag.length(), endIndex).trim();
    }
}
